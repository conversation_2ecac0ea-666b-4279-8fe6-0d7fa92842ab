import random

def decode_flag(scrambled_result, key):
    # Convert the scrambled result back into a list of integers
    scrambled_list = [int(scrambled_result[i:i+2], 16) for i in range(0, len(scrambled_result), 2)]
    
    # Determine the correct seed used for shuffling
    for seed in range(0, 11):
        random.seed(seed)
        
        # Create a list of chunk indices and shuffle them
        chunk_size = 4
        num_chunks = len(scrambled_list) // chunk_size
        chunk_indices = list(range(num_chunks))
        random.shuffle(chunk_indices)
        
        # Unshuffle the chunks
        unshuffled_chunks = [None] * num_chunks
        for i, chunk_index in enumerate(chunk_indices):
            unshuffled_chunks[chunk_index] = scrambled_list[i*chunk_size:(i+1)*chunk_size]
        
        # Flatten the unshuffled chunks
        unshuffled_list = [item for chunk in unshuffled_chunks for item in chunk]
        
        # XOR the result with the key to get the original flag
        flag = ''.join([chr(c ^ key) for c in unshuffled_list])
        
        # Check if the flag starts with "ENO"
        if flag.startswith("ENO"):
            return flag
    
    return None

def main():
    scrambled_result = "1e78197567121966196e757e1f69781e1e1f7e736d6d1f75196e75191b646e196f6465510b0b0b57"
    
    # Brute-force the key since it's unknown
    for key in range(256):
        flag = decode_flag(scrambled_result, key)
        if flag:
            print(f"Key: {key}, Flag: {flag}")
            break

if __name__ == "__main__":
    main()