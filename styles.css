/* ===== CSS RESET & BASE STYLES ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #000000;
    --secondary-color: #ffffff;
    --accent-color: #f5f5f5;
    --text-primary: #1a1a1a;
    --text-secondary: #666666;
    --text-light: #999999;
    --border-color: #e5e5e5;
    --shadow-light: 0 2px 20px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 8px 40px rgba(0, 0, 0, 0.15);
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-fast: all 0.2s ease;
    --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    --font-display: 'Playfair Display', serif;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: var(--font-primary);
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--secondary-color);
    overflow-x: hidden;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
}

/* ===== LOADING SCREEN ===== */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--secondary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity 0.5s ease, visibility 0.5s ease;
}

.loading-screen.hidden {
    opacity: 0;
    visibility: hidden;
}

.loading-content {
    text-align: center;
}

.loading-text {
    font-family: var(--font-display);
    font-size: 2rem;
    font-weight: 600;
    letter-spacing: 0.2em;
    margin-bottom: 2rem;
    color: var(--text-primary);
}

.loading-bar {
    width: 200px;
    height: 2px;
    background: var(--accent-color);
    position: relative;
    overflow: hidden;
}

.loading-progress {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--primary-color);
    animation: loading 2s ease-in-out infinite;
}

@keyframes loading {
    0% {
        left: -100%;
    }

    50% {
        left: 0%;
    }

    100% {
        left: 100%;
    }
}

/* ===== NAVIGATION ===== */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--border-color);
    z-index: 1000;
    transition: var(--transition-smooth);
    transform: translateY(0);
}

.navbar.hidden {
    transform: translateY(-100%);
}

.nav-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 2rem;
    max-width: 1400px;
    margin: 0 auto;
}

.nav-logo a {
    font-family: var(--font-display);
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    text-decoration: none;
    letter-spacing: 0.1em;
}

.nav-menu {
    display: flex;
    gap: 2rem;
}

.nav-link {
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 500;
    font-size: 0.9rem;
    letter-spacing: 0.05em;
    text-transform: uppercase;
    transition: var(--transition-fast);
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: var(--text-primary);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--text-primary);
    transition: var(--transition-fast);
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.nav-toggle span {
    width: 25px;
    height: 2px;
    background: var(--text-primary);
    transition: var(--transition-fast);
}

/* ===== HERO SECTION ===== */
.hero {
    position: relative;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -2;
}

.hero-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.4) 0%, rgba(0, 0, 0, 0.2) 100%);
    z-index: -1;
}

.hero-content {
    text-align: center;
    color: var(--secondary-color);
    z-index: 1;
}

.hero-title {
    margin-bottom: 1.5rem;
}

.hero-name {
    display: block;
    font-family: var(--font-display);
    font-size: clamp(3rem, 8vw, 6rem);
    font-weight: 600;
    line-height: 1.1;
    letter-spacing: 0.02em;
    margin-bottom: 0.5rem;
}

.hero-subtitle {
    display: block;
    font-family: var(--font-primary);
    font-size: clamp(1rem, 2.5vw, 1.5rem);
    font-weight: 300;
    letter-spacing: 0.3em;
    text-transform: uppercase;
    opacity: 0.9;
}

.hero-description {
    font-size: 1.1rem;
    font-weight: 300;
    max-width: 600px;
    margin: 0 auto 3rem;
    opacity: 0.9;
    line-height: 1.8;
}

.hero-scroll {
    position: absolute;
    bottom: 3rem;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
}

.scroll-indicator span {
    display: block;
    font-size: 0.8rem;
    letter-spacing: 0.1em;
    text-transform: uppercase;
    margin-bottom: 1rem;
    opacity: 0.8;
}

.scroll-line {
    width: 1px;
    height: 40px;
    background: var(--secondary-color);
    margin: 0 auto;
    opacity: 0.6;
    animation: scroll-bounce 2s infinite;
}

@keyframes scroll-bounce {

    0%,
    100% {
        transform: translateY(0);
        opacity: 0.6;
    }

    50% {
        transform: translateY(10px);
        opacity: 1;
    }
}

.hero-social {
    position: absolute;
    bottom: 3rem;
    right: 3rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.social-link {
    color: var(--secondary-color);
    text-decoration: none;
    font-size: 0.8rem;
    letter-spacing: 0.1em;
    text-transform: uppercase;
    opacity: 0.8;
    transition: var(--transition-fast);
    writing-mode: vertical-rl;
    text-orientation: mixed;
}

.social-link:hover {
    opacity: 1;
    transform: translateY(-2px);
}

/* ===== SECTION HEADERS ===== */
.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-title {
    font-family: var(--font-display);
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1rem;
    line-height: 1.2;
}

.section-subtitle {
    font-size: 1.1rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.8;
}

/* ===== PORTFOLIO SECTION ===== */
.portfolio {
    padding: 8rem 0;
    background: var(--secondary-color);
}

.bento-grid {
    display: grid;
    grid-template-columns: repeat(12, 1fr);
    grid-auto-rows: 300px;
    gap: 1.5rem;
    margin-top: 2rem;
}

.bento-item {
    position: relative;
    overflow: hidden;
    border-radius: 12px;
    cursor: pointer;
    transition: var(--transition-smooth);
}

.bento-item:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-medium);
}

.bento-large {
    grid-column: span 8;
    grid-row: span 2;
}

.bento-medium {
    grid-column: span 4;
    grid-row: span 2;
}

.bento-small {
    grid-column: span 4;
    grid-row: span 1;
}

.bento-wide {
    grid-column: span 8;
    grid-row: span 1;
}

.bento-tall {
    grid-column: span 4;
    grid-row: span 2;
}

.bento-square {
    grid-column: span 4;
    grid-row: span 1;
}

.bento-image {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.bento-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition-smooth);
}

.bento-item:hover .bento-image img {
    transform: scale(1.05);
}

.bento-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.7) 0%, rgba(0, 0, 0, 0.3) 100%);
    display: flex;
    align-items: flex-end;
    padding: 2rem;
    opacity: 0;
    transition: var(--transition-smooth);
}

.bento-item:hover .bento-overlay {
    opacity: 1;
}

.bento-content h3 {
    font-family: var(--font-display);
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--secondary-color);
    margin-bottom: 0.5rem;
}

.bento-content p {
    color: rgba(255, 255, 255, 0.9);
    font-size: 0.9rem;
    line-height: 1.6;
}

/* ===== ABOUT SECTION ===== */
.about {
    padding: 8rem 0;
    background: var(--accent-color);
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.about-text {
    padding-right: 2rem;
}

.about-description {
    font-size: 1.1rem;
    line-height: 1.8;
    color: var(--text-secondary);
    margin-bottom: 3rem;
}

.about-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
}

.stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-family: var(--font-display);
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.1em;
}

.about-image {
    position: relative;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: var(--shadow-light);
}

.about-image img {
    width: 100%;
    height: 500px;
    object-fit: cover;
}

/* ===== CONTACT SECTION ===== */
.contact {
    padding: 8rem 0;
    background: var(--secondary-color);
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    margin-top: 2rem;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 3rem;
}

.contact-item h3 {
    font-family: var(--font-display);
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.contact-item a {
    color: var(--text-secondary);
    text-decoration: none;
    font-size: 1.1rem;
    transition: var(--transition-fast);
}

.contact-item a:hover {
    color: var(--text-primary);
}

.social-links {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.contact-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-group {
    position: relative;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 1rem;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    font-family: var(--font-primary);
    font-size: 1rem;
    background: var(--secondary-color);
    transition: var(--transition-fast);
    resize: vertical;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--text-primary);
}

.btn-submit {
    padding: 1rem 2rem;
    background: var(--primary-color);
    color: var(--secondary-color);
    border: none;
    border-radius: 8px;
    font-family: var(--font-primary);
    font-size: 1rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    cursor: pointer;
    transition: var(--transition-fast);
}

.btn-submit:hover {
    background: var(--text-primary);
    transform: translateY(-2px);
    box-shadow: var(--shadow-light);
}

/* ===== FOOTER ===== */
.footer {
    padding: 3rem 0;
    background: var(--primary-color);
    color: var(--secondary-color);
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.footer-left p {
    font-size: 0.9rem;
    opacity: 0.8;
}

.footer-right a {
    color: var(--secondary-color);
    text-decoration: none;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    transition: var(--transition-fast);
}

.footer-right a:hover {
    opacity: 0.8;
}

/* ===== LIGHTBOX ===== */
.lightbox {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition-smooth);
}

.lightbox.active {
    opacity: 1;
    visibility: visible;
}

.lightbox-content {
    position: relative;
    max-width: 90vw;
    max-height: 90vh;
    text-align: center;
}

.lightbox-close {
    position: absolute;
    top: -50px;
    right: 0;
    color: var(--secondary-color);
    font-size: 2rem;
    cursor: pointer;
    transition: var(--transition-fast);
}

.lightbox-close:hover {
    opacity: 0.7;
}

.lightbox-content img {
    max-width: 100%;
    max-height: 80vh;
    object-fit: contain;
    border-radius: 8px;
}

.lightbox-caption {
    color: var(--secondary-color);
    margin-top: 1rem;
    font-size: 1rem;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1200px) {
    .container {
        padding: 0 1.5rem;
    }

    .bento-large {
        grid-column: span 12;
        grid-row: span 1;
    }

    .bento-medium,
    .bento-wide {
        grid-column: span 6;
        grid-row: span 1;
    }

    .bento-small,
    .bento-tall,
    .bento-square {
        grid-column: span 6;
        grid-row: span 1;
    }
}

@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        top: 100%;
        left: 0;
        width: 100%;
        background: var(--secondary-color);
        flex-direction: column;
        padding: 2rem;
        box-shadow: var(--shadow-light);
        transform: translateY(-100%);
        transition: var(--transition-smooth);
        opacity: 0;
        visibility: hidden;
    }

    .nav-menu.active {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }

    .nav-toggle {
        display: flex;
    }

    .nav-toggle.active span:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }

    .nav-toggle.active span:nth-child(2) {
        opacity: 0;
    }

    .nav-toggle.active span:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
    }

    .hero-social {
        position: static;
        flex-direction: row;
        justify-content: center;
        margin-top: 2rem;
    }

    .social-link {
        writing-mode: initial;
        text-orientation: initial;
    }

    .bento-grid {
        grid-template-columns: 1fr;
        grid-auto-rows: 250px;
        gap: 1rem;
    }

    .bento-item {
        grid-column: span 1 !important;
        grid-row: span 1 !important;
    }

    .about-content,
    .contact-content {
        grid-template-columns: 1fr;
        gap: 3rem;
    }

    .about-text {
        padding-right: 0;
    }

    .about-stats {
        grid-template-columns: repeat(3, 1fr);
        gap: 1rem;
    }

    .footer-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 1rem;
    }

    .hero-scroll {
        bottom: 2rem;
    }

    .hero-social {
        bottom: 2rem;
        right: 2rem;
    }

    .section-header {
        margin-bottom: 3rem;
    }

    .portfolio,
    .about,
    .contact {
        padding: 4rem 0;
    }

    .about-stats {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .bento-grid {
        grid-auto-rows: 200px;
    }

    .bento-overlay {
        padding: 1rem;
    }

    .bento-content h3 {
        font-size: 1.2rem;
    }

    .lightbox-content {
        max-width: 95vw;
        max-height: 95vh;
    }

    .lightbox-close {
        top: -40px;
        font-size: 1.5rem;
    }
}

/* ===== ANIMATIONS ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }

    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }

    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: var(--transition-smooth);
}

.animate-on-scroll.animated {
    opacity: 1;
    transform: translateY(0);
}

/* ===== UTILITY CLASSES ===== */
.text-center {
    text-align: center;
}

.mb-0 {
    margin-bottom: 0;
}

.mb-1 {
    margin-bottom: 1rem;
}

.mb-2 {
    margin-bottom: 2rem;
}

.mb-3 {
    margin-bottom: 3rem;
}

.mt-0 {
    margin-top: 0;
}

.mt-1 {
    margin-top: 1rem;
}

.mt-2 {
    margin-top: 2rem;
}

.mt-3 {
    margin-top: 3rem;
}

/* ===== PERFORMANCE OPTIMIZATIONS ===== */
.bento-image img,
.about-image img,
.hero-image {
    will-change: transform;
}

.bento-item,
.nav-link,
.social-link,
.btn-submit {
    will-change: transform;
}

/* ===== ACCESSIBILITY ===== */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .scroll-line {
        animation: none;
    }

    .loading-progress {
        animation: none;
    }
}

/* ===== FOCUS STYLES ===== */
.nav-link:focus,
.social-link:focus,
.btn-submit:focus,
.form-group input:focus,
.form-group textarea:focus {
    outline: 2px solid var(--text-primary);
    outline-offset: 2px;
}

/* ===== PRINT STYLES ===== */
@media print {

    .navbar,
    .hero-social,
    .hero-scroll,
    .contact-form,
    .footer {
        display: none;
    }

    .hero {
        height: auto;
        padding: 2rem 0;
    }

    .portfolio,
    .about,
    .contact {
        padding: 2rem 0;
    }
}